# Java 17 兼容性修复说明

## 问题描述
在Java 17环境下运行Spring Boot应用时，出现以下错误：
```
java.lang.reflect.InaccessibleObjectException: Unable to make protected final java.lang.Class java.lang.ClassLoader.defineClass(...) accessible: module java.base does not "opens java.lang" to unnamed module
```

## 问题原因
Java 9引入了模块系统，Java 17进一步加强了访问控制。CGLIB、Spring等框架需要访问Java内部API，但被模块系统阻止。

## 解决方案

### 1. Maven配置修复
已在根目录`pom.xml`中进行了以下修改：

- 更新Maven编译器插件到3.11.0版本
- 更新Java版本配置为17
- 添加Lombok版本管理(1.18.26)
- 在编译器和测试插件中添加`--add-opens`参数

### 2. 运行时JVM参数
需要在启动应用时添加以下JVM参数：

```bash
--add-opens java.base/java.lang=ALL-UNNAMED
--add-opens java.base/java.util=ALL-UNNAMED
--add-opens java.base/java.lang.reflect=ALL-UNNAMED
--add-opens java.base/java.text=ALL-UNNAMED
--add-opens java.desktop/java.awt.font=ALL-UNNAMED
--add-opens java.base/java.lang.invoke=ALL-UNNAMED
--add-opens java.base/java.security=ALL-UNNAMED
--add-opens java.base/java.net=ALL-UNNAMED
--add-opens java.base/java.io=ALL-UNNAMED
--add-opens java.base/java.nio=ALL-UNNAMED
--add-opens java.base/java.util.concurrent=ALL-UNNAMED
--add-opens java.base/sun.nio.ch=ALL-UNNAMED
--add-opens java.base/sun.security.util=ALL-UNNAMED
--add-opens java.base/sun.security.x509=ALL-UNNAMED
```

### 3. 使用方法

#### 方法1：使用提供的启动脚本
```bash
# Windows
start-insurance-app.bat

# Linux/Mac
./start-app.sh sfbx-insurance/insurance-app/target/insurance-app.jar
```

#### 方法2：IDE配置
在IDE的运行配置中，将上述JVM参数添加到VM options中。

#### 方法3：命令行启动
```bash
java --add-opens java.base/java.lang=ALL-UNNAMED \
     --add-opens java.base/java.util=ALL-UNNAMED \
     --add-opens java.base/java.lang.reflect=ALL-UNNAMED \
     --add-opens java.base/java.text=ALL-UNNAMED \
     --add-opens java.desktop/java.awt.font=ALL-UNNAMED \
     --add-opens java.base/java.lang.invoke=ALL-UNNAMED \
     --add-opens java.base/java.security=ALL-UNNAMED \
     --add-opens java.base/java.net=ALL-UNNAMED \
     --add-opens java.base/java.io=ALL-UNNAMED \
     --add-opens java.base/java.nio=ALL-UNNAMED \
     --add-opens java.base/java.util.concurrent=ALL-UNNAMED \
     --add-opens java.base/sun.nio.ch=ALL-UNNAMED \
     --add-opens java.base/sun.security.util=ALL-UNNAMED \
     --add-opens java.base/sun.security.x509=ALL-UNNAMED \
     -jar your-app.jar
```

#### 方法4：Docker环境
在Dockerfile中设置JAVA_OPTS环境变量：
```dockerfile
ENV JAVA_OPTS="--add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.util=ALL-UNNAMED ..."
```

### 4. 编译项目
```bash
mvn clean package -DskipTests
```

### 5. 验证修复
启动应用后，如果不再出现`InaccessibleObjectException`错误，说明修复成功。

## 注意事项
1. 这些参数会降低Java模块系统的安全性，但对于现有的Spring Boot应用是必要的
2. 建议在生产环境中也使用相同的JVM参数
3. 未来版本的Spring Boot和相关框架可能会提供更好的Java 17+支持

## 相关文件
- `java17-jvm-args.txt` - JVM参数列表
- `start-app.sh` - Linux/Mac启动脚本
- `start-app.bat` - Windows启动脚本
- `start-insurance-app.bat` - 保险应用专用启动脚本
