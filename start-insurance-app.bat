@echo off
REM 保险应用启动脚本 - Java 11 版本

echo 启动保险应用 (insurance-app)...

REM 应用特定参数
set APP_ARGS=-Dspring.profiles.active=dev ^
-Dserver.port=7065 ^
-Dfile.encoding=UTF-8

REM JAR文件路径
set JAR_FILE=sfbx-insurance\insurance-app\target\insurance-app.jar

REM 检查JAR文件是否存在
if not exist "%JAR_FILE%" (
    echo 错误: JAR文件 '%JAR_FILE%' 不存在
    echo 请先执行 mvn clean package 编译项目
    pause
    exit /b 1
)

echo 使用Java 11启动...
java %APP_ARGS% -jar "%JAR_FILE%"

pause
